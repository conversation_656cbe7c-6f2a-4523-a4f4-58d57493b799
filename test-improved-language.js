const { companyInfo } = require('./data/focepData');

console.log('Test des améliorations du langage du chatbot FOCEP SA\n');

// Simuler les réponses améliorées
const improvedResponses = {
  fr: {
    greeting: [
      "🌟 Bonjour et bienvenue chez FOCEP SA ! Je suis votre assistant virtuel personnel, ravi de vous accompagner aujourd'hui. Comment puis-je vous être utile pour répondre à vos besoins financiers ?",
      "✨ Bienvenue dans l'univers FOCEP SA ! Je suis là pour vous offrir un service de qualité exceptionnelle et répondre à toutes vos questions avec précision. Que puis-je faire pour vous satisfaire ?",
      "🤝 Bonjour ! C'est un plaisir de vous accueillir chez FOCEP SA. Je suis votre conseiller virtuel dédié, prêt à vous assister dans toutes vos démarches financières avec professionnalisme."
    ],
    unknown: [
      "🤔 Je vous présente mes excuses, mais je n'ai pas parfaitement saisi votre demande. <PERSON><PERSON><PERSON><PERSON>-vous, s'il vous plaît, reformuler votre question de manière plus précise ? Je serai ravi de vous offrir une réponse adaptée à vos besoins.",
      "💭 Pardonnez-moi, mais votre demande nécessite une clarification pour que je puisse vous servir au mieux. Pourriez-vous m'expliquer plus en détail ce que vous recherchez ? Je ferai tout mon possible pour vous satisfaire.",
      "🔍 Pour vous offrir le meilleur service possible, j'aimerais mieux comprendre votre besoin. Pourriez-vous préciser votre demande ou choisir une option dans le menu principal ci-dessous ?"
    ],
    thanks: [
      "🙏 Je vous en prie, c'est avec grand plaisir ! N'hésitez surtout pas si vous avez d'autres questions. Je reste entièrement à votre disposition.",
      "😊 C'est avec joie que je vous ai aidé ! Y a-t-il autre chose que je puisse faire pour améliorer votre expérience avec FOCEP SA ?",
      "✨ De rien, c'est tout naturel ! Je demeure à votre service pour toute autre demande ou information complémentaire."
    ],
    goodbye: [
      "👋 Au revoir et merci infiniment d'avoir choisi FOCEP SA ! Passez une excellente journée et n'hésitez pas à revenir vers nous à tout moment.",
      "🌟 À très bientôt ! Nous restons toujours disponibles pour vous accompagner dans vos projets financiers. Excellente continuation !",
      "🤝 Bonne journée ! Nous sommes fiers de vous compter parmi nos clients et restons à votre écoute pour tous vos besoins futurs."
    ]
  }
};

// Test des réponses améliorées
console.log('=== RÉPONSES D\'ACCUEIL AMÉLIORÉES ===');
improvedResponses.fr.greeting.forEach((greeting, index) => {
  console.log(`${index + 1}. ${greeting}\n`);
});

console.log('=== RÉPONSES POUR DEMANDES NON COMPRISES ===');
improvedResponses.fr.unknown.forEach((unknown, index) => {
  console.log(`${index + 1}. ${unknown}\n`);
});

console.log('=== RÉPONSES DE REMERCIEMENT ===');
improvedResponses.fr.thanks.forEach((thanks, index) => {
  console.log(`${index + 1}. ${thanks}\n`);
});

console.log('=== RÉPONSES D\'AU REVOIR ===');
improvedResponses.fr.goodbye.forEach((goodbye, index) => {
  console.log(`${index + 1}. ${goodbye}\n`);
});

// Test de la réponse de contact améliorée
console.log('=== RÉPONSE DE CONTACT AMÉLIORÉE ===');
const contactResponse = `📞 *Contacter FOCEP SA - Nous sommes ravis de vous servir !*

🌟 *Notre équipe dévouée est à votre entière disposition*

📱 *Téléphone :* ${companyInfo.contact.phone}
📧 *Email :* ${companyInfo.contact.email}
🌐 *Site web :* ${companyInfo.contact.website}
📍 *Siège social :* ${companyInfo.headquarters}

🏦 *Notre réseau étendu :*
• ${companyInfo.totalAgencies} agences stratégiquement implantées
• Présent dans ${companyInfo.regionsPresent.length} régions : ${companyInfo.regionsPresent.join(', ')}
• Fiers de servir plus de ${companyInfo.currentClients.toLocaleString()} clients satisfaits

⏰ *Horaires d'ouverture :*
• Lundi au vendredi : 8h00 - 17h00
• Samedi : 8h00 - 12h00

💡 *Nous sommes toujours là pour vous aider à atteindre vos objectifs financiers !*`;

console.log(contactResponse);

// Test du menu principal amélioré
console.log('\n=== MENU PRINCIPAL AMÉLIORÉ ===');
const menuText = "🎯 Je suis à votre entière disposition pour vous accompagner. Voici les services que je peux vous proposer avec plaisir :";
console.log(menuText);

const menuButtons = [
  "📊 Informations compte",
  "💳 Prêts & crédits", 
  "🏦 Nos services",
  "📞 Nous contacter",
  "🏛️ À propos de FOCEP",
  "🏢 Nos agences"
];

menuButtons.forEach((button, index) => {
  console.log(`${index + 1}. ${button}`);
});

// Test de la réponse d'erreur améliorée
console.log('\n=== RÉPONSE D\'ERREUR AMÉLIORÉE ===');
const errorResponse = `🔧 *Nous vous présentons nos sincères excuses pour ce désagrément*

Notre équipe technique travaille avec diligence pour résoudre ce problème. Veuillez réessayer dans quelques instants, ou n'hésitez pas à contacter notre équipe de support dédiée qui sera ravie de vous assister personnellement.

📞 Contact : ${companyInfo.contact.phone}
Merci pour votre patience et votre compréhension.`;

console.log(errorResponse);

console.log('\n=== RÉSUMÉ DES AMÉLIORATIONS ===');
console.log('✅ Langage plus poli et professionnel');
console.log('✅ Utilisation d\'émojis pour rendre les réponses plus attrayantes');
console.log('✅ Formulations plus chaleureuses et personnalisées');
console.log('✅ Expressions de politesse renforcées');
console.log('✅ Ton plus empathique et orienté service client');
console.log('✅ Réponses plus détaillées et informatives');
console.log('✅ Meilleure structuration des informations');
console.log('✅ Langage adapté au contexte bancaire/financier');

console.log('\nTest terminé - Le chatbot FOCEP SA a maintenant un langage amélioré !');
